# GoResoDownload

GoResoDownload is a media file download and management system for real estate listings. It handles media file downloads, updates, and deletions for different board types.

## Features

- Hierarchical directory structure (L1/L2) based on dates and board types
- Automatic UUID5 generation for L2 directories
- MongoDB integration for directory mapping storage
- Support for multiple board types (TRB, DDF, BRE, CLG, OTW, EDM, CAR, USER)
- Directory statistics tracking
- Temporary directory mapping with automatic cleanup
- Media file download and management for multiple board types (CAR, DDF, BRE, EDM, TRB)
- Automatic thumbnail generation
- Concurrent download and deletion processing
- MongoDB change stream monitoring
- Directory structure management (L1/L2)
- Failed task tracking and retry mechanism
- Process monitoring and status tracking



## Directory Structure

The system uses a two-level directory structure:
- L1: Year-week based folders (e.g., 750, 751, etc.)
- L2: UUID5-based subfolders (e.g., "abc12", "def34", etc.)

### L1 Calculation
- Each year has 50 folders (weeks)
- Weeks 0 and 49+ are merged into one folder
- 2015 starts at 750
- 2024 maps to 1200
- 2025 maps to 1250

### Board Types and L2 Sizes
- TRB: 512 L2 directories
- DDF: 1024 L2 directories
- BRE: 64 L2 directories
- CLG: 64 L2 directories
- OTW: 64 L2 directories
- EDM: 64 L2 directories
- CAR: 256 L2 directories
- USER: 512 L2 directories

## Usage

### Basic Usage

```shell
cd goresodownload
go mod tidy


cd rmconfig
# dryrun
./start.sh -n goresodownloadCAR -d "goresodownload" -cmd "cmd/goresodownload/main.go -board CAR -force -dryrun"
# run board: CAR|DDF|BRE|EDM|TRB
./start.sh  -t batch -n goresodownloadCAR -d "goresodownload" -cmd "cmd/goresodownload/main.go -board CAR -force"


```

### Configuration

The package requires the following configuration:
0. local.ini config
```
[imageStore]
reso_treb_image_dir = ["/tmp/trb_images","/tmp/trb_images2"]
reso_car_image_dir = ["/tmp/car_images"]
reso_crea_image_dir = ["/tmp/ddf_images"]
reso_bcre_image_dir = ["/tmp/bre_images"]
reso_edm_image_dir = ["/tmp/edm_images"]
```

1. MongoDB connection for:
   - Watch collections (board-specific)
   - Merged collections
   - Failed tasks collection
   - Process status collection
   - System data collection

2. Storage paths for each board type

3. Downloader configuration:
   - Download concurrency
   - Delete concurrency
   - Max retries
   - Consecutive failures threshold

## Testing

Run end-to-end tests with:

```bash
go test -v ./test/e2e
```

Test coverage can be checked with:

```bash
go test -cover ./...
```

## Dependencies

- github.com/real-rm/gobase
- github.com/real-rm/gofile
- github.com/real-rm/gofile/levelStore
- github.com/real-rm/golog
- github.com/real-rm/gomongo
- github.com/real-rm/goprocess
- github.com/real-rm/gowatch
- go.mongodb.org/mongo-driver/mongo


## Merged saved
1. DB:
phoHL:[int32,int32]
tnHL: int32
docHL:["int32.pdf","int32.mp3"] 


2. pic dir: L1/L2/sid_base62.jpg
1274/ffc41/C11910492_B8P8Uz.jpg


```
step1: get L1/L2 using ts,board,sid
path, err := levelStore.GetFullFilePath(propTs, "TRB", "********")
if err != nil {
    // Handle error
}
fmt.Println(path) // Output: /1200/abc12

step2: get hash int32
// keyStr is the media key, for treb: key, other board: MediaKey.
hash := levelStore.MurmurToInt32(keyStr) // Output(int32): 2004

step3: get base62
base62, err := levelStore.Int32ToBase62(hash)
```

## 根据merged信息，获取图片链接
1. 获取本地路径，rmconfig 中获取
[imageStore]
reso_bcre_image_dir = [ ]
reso_car_image_dir = [ ]
reso_crea_image_dir = [ ]
reso_edm_image_dir = [ ]
reso_treb_image_dir = [ ]

2. 根据prop的 ts,board,sid信息，计算L1，L2
3. 根据prop的phoHL，tnHL，拼接出图片路径 
L1/L2/sid_base62.jpg

