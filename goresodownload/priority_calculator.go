package goresodownload

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	golog "github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

// NormalizedProperty holds normalized property data across different boards
type NormalizedProperty struct {
	HasPhoto     bool
	MlsStatus    string
	OnD          *time.Time // ListingContractDate or OriginalEntryTimestamp
	Region       string     // CountyOrParish, CityRegion, StateRegion
	PropertyType string
	Province     string // StateOrProvince
	DOM          int    // DaysOnMarket or calculated
}

// CalculatePriority calculates the priority for a property based on the CoffeeScript logic
// @param record - property record from change stream
// @param boardType - board type (TRB, BRE, DDF, CLG, EDM, CAR)
// @param existMergedProp - existing merged property record (can be nil)
// @returns priority score, higher score = higher priority
func CalculatePriority(record bson.M, boardType string, existMergedProp bson.M) int {
	priority := 0

	// Normalize property data based on board type
	normalized, err := normalizePropertyData(record, boardType)
	if err != nil {
		golog.Error("Failed to normalize property data", "error", err, "boardType", boardType)
		return 1000 // fallback to default priority
	}

	// 1. No photos (phoUrls missing or empty) - highest priority
	hasPhoto := checkHasPhoto(existMergedProp, boardType)
	if !hasPhoto {
		priority += 30000
	}

	// 2. Active status bonus
	if isActiveStatus(normalized.MlsStatus) {
		priority += 10000
	}

	// 3. Current year listing bonus
	if normalized.OnD != nil && normalized.OnD.Year() == time.Now().Year() {
		priority += 5000
	}

	// 4. GTA region bonus
	if isGTARegion(normalized.Region) {
		priority += 500
	}

	// 5. New listing bonus (within 3 days)
	if normalized.OnD != nil && time.Since(*normalized.OnD) < 3*24*time.Hour {
		priority += 300
	}

	// 6. Residential/condo property type bonus
	if isResidentialProperty(normalized.PropertyType) {
		priority += 200
	}

	// 7. Status-specific bonuses
	if strings.ToLower(normalized.MlsStatus) == "sold" {
		priority += 50
	} else if strings.ToLower(normalized.MlsStatus) == "leased" {
		priority += 30
	}

	// 8. Ontario province bonus
	if normalized.Province == "ON" {
		priority += 20
	}

	// 9. Days on market bonus (0-30 days)
	if normalized.DOM >= 0 && normalized.DOM <= 30 {
		priority += 30 - normalized.DOM
	}

	return priority
}

// normalizePropertyData converts board-specific fields to normalized format
func normalizePropertyData(record bson.M, boardType string) (*NormalizedProperty, error) {
	switch boardType {
	case "TRB":
		return normalizeTRBData(record)
	case "BRE":
		return normalizeBREData(record)
	case "DDF":
		return normalizeDDFData(record)
	case "CLG":
		return normalizeCLGData(record)
	case "EDM":
		return normalizeEDMData(record)
	case "CAR":
		return normalizeCARData(record)
	default:
		return nil, fmt.Errorf("unsupported board type: %s", boardType)
	}
}

// checkHasPhoto checks if the merged property has photos
func checkHasPhoto(existMergedProp bson.M, boardType string) bool {
	if existMergedProp == nil {
		return false
	}

	// For TRB, check phoUrls field
	if boardType == "TRB" {
		if phoUrls, ok := existMergedProp["phoUrls"]; ok && phoUrls != nil {
			return true
		}
		// Also check media field
		if media, ok := existMergedProp["media"]; ok {
			if mediaArray, ok := media.([]interface{}); ok && len(mediaArray) > 0 {
				return true
			}
		}
		return false
	}

	// For other boards, check Media field
	if media, ok := existMergedProp["Media"]; ok {
		if mediaArray, ok := media.([]interface{}); ok && len(mediaArray) > 0 {
			return true
		}
	}

	return false
}

// isActiveStatus checks if the status indicates an active listing
func isActiveStatus(status string) bool {
	if status == "" {
		return false
	}
	lowerStatus := strings.ToLower(status)
	return lowerStatus == "a" || lowerStatus == "active" || lowerStatus == "new"
}

// isGTARegion checks if the region is in the Greater Toronto Area
func isGTARegion(region string) bool {
	if region == "" {
		return false
	}
	gtaPattern := regexp.MustCompile(`(?i)(toronto|peel|york|durham|halton)`)
	return gtaPattern.MatchString(region)
}

// isResidentialProperty checks if the property type is residential or condo
func isResidentialProperty(propertyType string) bool {
	if propertyType == "" {
		return false
	}
	residentialPattern := regexp.MustCompile(`(?i)(residential|condo)`)
	return residentialPattern.MatchString(propertyType)
}

// parseDate attempts to parse a date from various formats
func parseDate(dateValue interface{}) *time.Time {
	if dateValue == nil {
		return nil
	}

	switch v := dateValue.(type) {
	case time.Time:
		return &v
	case string:
		// Try common date formats
		formats := []string{
			"2006-01-02",
			"2006-01-02T15:04:05Z",
			"2006-01-02T15:04:05.000Z",
			"2006-01-02T15:04:05-07:00",
		}
		for _, format := range formats {
			if t, err := time.Parse(format, v); err == nil {
				return &t
			}
		}
	}
	return nil
}

// parseInt safely converts interface{} to int
func parseInt(value interface{}) int {
	if value == nil {
		return -1
	}

	switch v := value.(type) {
	case int:
		return v
	case int32:
		return int(v)
	case int64:
		return int(v)
	case float64:
		return int(v)
	case string:
		if i, err := strconv.Atoi(v); err == nil {
			return i
		}
	}
	return -1
}

// getString safely converts interface{} to string
func getString(value interface{}) string {
	if value == nil {
		return ""
	}
	if s, ok := value.(string); ok {
		return s
	}
	return ""
}

// GetExistingMergedProperty fetches the existing merged property from database
func GetExistingMergedProperty(propID string, boardType string) (bson.M, error) {
	if propID == "" || boardType == "" {
		return nil, fmt.Errorf("propID and boardType are required")
	}

	tableName, exists := BoardMergedTable[boardType]
	if !exists {
		return nil, fmt.Errorf("unknown board type: %s", boardType)
	}

	collection := gomongo.Coll("rni", tableName)
	ctx := context.Background()

	var result bson.M
	err := collection.FindOne(ctx, bson.M{"_id": propID}).Decode(&result)
	if err != nil {
		// Document not found is not an error for priority calculation
		if err.Error() == "mongo: no documents in result" {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to fetch merged property: %w", err)
	}

	return result, nil
}

// Board-specific normalization functions

// normalizeTRBData normalizes TRB board data
func normalizeTRBData(record bson.M) (*NormalizedProperty, error) {
	normalized := &NormalizedProperty{}

	// MlsStatus -> status
	normalized.MlsStatus = getString(record["MlsStatus"])

	// ListingContractDate -> onD, OriginalEntryTimestamp -> ts
	if onD := parseDate(record["ListingContractDate"]); onD != nil {
		normalized.OnD = onD
	}

	// CountyOrParish -> region
	normalized.Region = getString(record["CountyOrParish"])

	// PropertyType -> ptype
	normalized.PropertyType = getString(record["PropertyType"])

	// StateOrProvince -> prov
	normalized.Province = getString(record["StateOrProvince"])

	// DaysOnMarket -> dom
	normalized.DOM = parseInt(record["DaysOnMarket"])

	return normalized, nil
}

// normalizeBREData normalizes BRE board data
func normalizeBREData(record bson.M) (*NormalizedProperty, error) {
	normalized := &NormalizedProperty{}

	// ListingContractDate -> onD, ts -> ts
	if onD := parseDate(record["ListingContractDate"]); onD != nil {
		normalized.OnD = onD
	}

	// CountyOrParish -> region
	normalized.Region = getString(record["CountyOrParish"])

	// PropertyType -> ptype
	normalized.PropertyType = getString(record["PropertyType"])

	// StateOrProvince -> prov
	normalized.Province = getString(record["StateOrProvince"])

	// DOM calculated from OffMarketDate, ListingContractDate
	normalized.DOM = calculateDOMFromDates(record["OffMarketDate"], record["ListingContractDate"])

	return normalized, nil
}

// normalizeDDFData normalizes DDF board data
func normalizeDDFData(record bson.M) (*NormalizedProperty, error) {
	normalized := &NormalizedProperty{}

	// MlsStatus -> status
	normalized.MlsStatus = getString(record["MlsStatus"])

	// OriginalEntryTimestamp -> onD and ts
	normalized.OnD = parseDate(record["OriginalEntryTimestamp"])

	// CityRegion -> region (could also be cmty)
	normalized.Region = getString(record["CityRegion"])

	// PropertySubType -> ptype (needs special calculation)
	normalized.PropertyType = getString(record["PropertySubType"])

	// StateOrProvince -> prov
	normalized.Province = getString(record["StateOrProvince"])

	// DOM calculated from OriginalEntryTimestamp, ExpirationDate
	normalized.DOM = calculateDOMFromDates(record["OriginalEntryTimestamp"], record["ExpirationDate"])

	return normalized, nil
}

// normalizeCLGData normalizes CLG board data
func normalizeCLGData(record bson.M) (*NormalizedProperty, error) {
	normalized := &NormalizedProperty{}

	// MlsStatus -> status
	normalized.MlsStatus = getString(record["MlsStatus"])

	// ListingContractDate -> onD, ts -> ts
	if onD := parseDate(record["ListingContractDate"]); onD != nil {
		normalized.OnD = onD
	} else {
		normalized.OnD = parseDate(record["ts"])
	}

	// CountyOrParish -> region
	normalized.Region = getString(record["CountyOrParish"])

	// PropertyType -> ptype
	normalized.PropertyType = getString(record["PropertyType"])

	// StateOrProvince -> prov
	normalized.Province = getString(record["StateOrProvince"])

	// DaysOnMarket -> dom
	normalized.DOM = parseInt(record["DaysOnMarket"])

	return normalized, nil
}

// normalizeEDMData normalizes EDM board data
func normalizeEDMData(record bson.M) (*NormalizedProperty, error) {
	normalized := &NormalizedProperty{}

	// MlsStatus -> status
	normalized.MlsStatus = getString(record["MlsStatus"])

	// ListingContractDate -> onD (ts field deleted in mapping)
	normalized.OnD = parseDate(record["ListingContractDate"])

	// StateRegion -> region
	normalized.Region = getString(record["StateRegion"])

	// PropertyType -> ptype
	normalized.PropertyType = getString(record["PropertyType"])

	// StateOrProvince -> prov
	normalized.Province = getString(record["StateOrProvince"])

	// CumulativeDaysOnMarket -> dom
	normalized.DOM = parseInt(record["CumulativeDaysOnMarket"])

	return normalized, nil
}

// normalizeCARData normalizes CAR board data
func normalizeCARData(record bson.M) (*NormalizedProperty, error) {
	normalized := &NormalizedProperty{}

	// MlsStatus -> status
	normalized.MlsStatus = getString(record["MlsStatus"])

	// ListingContractDate -> onD (ts field deleted in mapping)
	normalized.OnD = parseDate(record["ListingContractDate"])

	// CountyOrParish -> region
	normalized.Region = getString(record["CountyOrParish"])

	// PropertyType -> ptype
	normalized.PropertyType = getString(record["PropertyType"])

	// StateOrProvince -> prov
	normalized.Province = getString(record["StateOrProvince"])

	// DaysOnMarket -> dom
	normalized.DOM = parseInt(record["DaysOnMarket"])

	return normalized, nil
}

// Helper functions

// calculateDOMFromDates calculates days on market from start and end dates
func calculateDOMFromDates(startDate, endDate interface{}) int {
	start := parseDate(startDate)
	end := parseDate(endDate)

	if start == nil {
		return -1
	}

	var endTime time.Time
	if end != nil {
		endTime = *end
	} else {
		endTime = time.Now()
	}

	duration := endTime.Sub(*start)
	days := int(duration.Hours() / 24)

	if days < 0 {
		return -1
	}

	return days
}

// formatDDFPropertyType formats DDF PropertySubType according to impMappingCreaReso.coffee logic
func formatDDFPropertyType(propertySubType interface{}) string {
	subType := getString(propertySubType)
	if subType == "" {
		return ""
	}

	// This is a simplified version - the actual logic in impMappingCreaReso.coffee
	// would be more complex. For now, return the subtype as-is.
	// TODO: Implement the full formatPropertyType logic from impMappingCreaReso.coffee
	return subType
}
