package goresodownload

import (
	"testing"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

func TestCalculatePriority(t *testing.T) {
	tests := []struct {
		name             string
		record           bson.M
		boardType        string
		existMergedProp  bson.M
		expectedPriority int
		description      string
	}{
		{
			name:      "TRB - No photos, active, current year, GTA, new listing",
			boardType: "TRB",
			record: bson.M{
				"MlsStatus":           "Active",
				"ListingContractDate": time.Now().Format("2006-01-02"),
				"CountyOrParish":      "Toronto",
				"PropertyType":        "Residential",
				"StateOrProvince":     "ON",
				"DaysOnMarket":        5,
			},
			existMergedProp:  nil,                                              // No existing merged prop = no photos
			expectedPriority: 30000 + 10000 + 5000 + 500 + 300 + 200 + 20 + 25, // 46045
			description:      "Should get maximum priority for new active residential listing in Toronto with no photos",
		},
		{
			name:      "TRB - Has photos, sold status",
			boardType: "TRB",
			record: bson.M{
				"MlsStatus":           "Sold",
				"ListingContractDate": "2023-01-01",
				"CountyOrParish":      "Toronto",
				"PropertyType":        "Residential",
				"StateOrProvince":     "ON",
				"DaysOnMarket":        15,
			},
			existMergedProp: bson.M{
				"media": []interface{}{
					bson.M{"key": "photo1", "url": "http://example.com/1.jpg"},
				},
			},
			expectedPriority: 500 + 200 + 50 + 20 + 15, // 785
			description:      "Should get lower priority for sold listing with photos",
		},
		{
			name:      "CAR - Active listing, no region bonus",
			boardType: "CAR",
			record: bson.M{
				"MlsStatus":           "Active",
				"ListingContractDate": time.Now().Format("2006-01-02"),
				"CountyOrParish":      "Waterloo",
				"PropertyType":        "Condo",
				"StateOrProvince":     "ON",
				"DaysOnMarket":        10,
			},
			existMergedProp: bson.M{
				"Media": []interface{}{
					bson.M{"MediaKey": "photo1", "MediaURL": "http://example.com/1.jpg"},
				},
			},
			expectedPriority: 10000 + 5000 + 200 + 20 + 20, // 15240, but might get +300 for new listing if within 3 days
			description:      "Should get active status and current year bonus but no GTA bonus",
		},
		{
			name:      "DDF - Leased status, old listing",
			boardType: "DDF",
			record: bson.M{
				"MlsStatus":              "Leased",
				"OriginalEntryTimestamp": "2022-06-01",
				"CityRegion":             "Durham",
				"PropertySubType":        "Condo",
				"StateOrProvince":        "ON",
			},
			existMergedProp:  nil,
			expectedPriority: 30000 + 500 + 200 + 30 + 20, // 30750
			description:      "Should get no photos bonus and leased status bonus",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			priority := CalculatePriority(tt.record, tt.boardType, tt.existMergedProp)

			// Allow some tolerance for time-based calculations (new listing bonus, etc.)
			tolerance := 350
			if abs(priority-tt.expectedPriority) > tolerance {
				t.Errorf("CalculatePriority() = %d, expected around %d (±%d), description: %s",
					priority, tt.expectedPriority, tolerance, tt.description)
			}
		})
	}
}

func TestNormalizePropertyData(t *testing.T) {
	tests := []struct {
		name      string
		record    bson.M
		boardType string
		expected  *NormalizedProperty
	}{
		{
			name:      "TRB normalization",
			boardType: "TRB",
			record: bson.M{
				"MlsStatus":           "Active",
				"ListingContractDate": "2024-01-15",
				"CountyOrParish":      "Toronto",
				"PropertyType":        "Residential",
				"StateOrProvince":     "ON",
				"DaysOnMarket":        10,
			},
			expected: &NormalizedProperty{
				MlsStatus:    "Active",
				Region:       "Toronto",
				PropertyType: "Residential",
				Province:     "ON",
				DOM:          10,
			},
		},
		{
			name:      "CAR normalization",
			boardType: "CAR",
			record: bson.M{
				"MlsStatus":           "Sold",
				"ListingContractDate": "2024-02-01",
				"CountyOrParish":      "Waterloo",
				"PropertyType":        "Condo",
				"StateOrProvince":     "ON",
				"DaysOnMarket":        25,
			},
			expected: &NormalizedProperty{
				MlsStatus:    "Sold",
				Region:       "Waterloo",
				PropertyType: "Condo",
				Province:     "ON",
				DOM:          25,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := normalizePropertyData(tt.record, tt.boardType)
			if err != nil {
				t.Fatalf("normalizePropertyData() error = %v", err)
			}

			if result.MlsStatus != tt.expected.MlsStatus {
				t.Errorf("MlsStatus = %v, expected %v", result.MlsStatus, tt.expected.MlsStatus)
			}
			if result.Region != tt.expected.Region {
				t.Errorf("Region = %v, expected %v", result.Region, tt.expected.Region)
			}
			if result.PropertyType != tt.expected.PropertyType {
				t.Errorf("PropertyType = %v, expected %v", result.PropertyType, tt.expected.PropertyType)
			}
			if result.Province != tt.expected.Province {
				t.Errorf("Province = %v, expected %v", result.Province, tt.expected.Province)
			}
			if result.DOM != tt.expected.DOM {
				t.Errorf("DOM = %v, expected %v", result.DOM, tt.expected.DOM)
			}
		})
	}
}

func TestIsActiveStatus(t *testing.T) {
	tests := []struct {
		status   string
		expected bool
	}{
		{"Active", true},
		{"active", true},
		{"ACTIVE", true},
		{"New", true},
		{"new", true},
		{"a", true},
		{"A", true},
		{"Sold", false},
		{"Expired", false},
		{"", false},
	}

	for _, tt := range tests {
		t.Run(tt.status, func(t *testing.T) {
			result := isActiveStatus(tt.status)
			if result != tt.expected {
				t.Errorf("isActiveStatus(%q) = %v, expected %v", tt.status, result, tt.expected)
			}
		})
	}
}

func TestIsGTARegion(t *testing.T) {
	tests := []struct {
		region   string
		expected bool
	}{
		{"Toronto", true},
		{"toronto", true},
		{"TORONTO", true},
		{"Peel", true},
		{"York", true},
		{"Durham", true},
		{"Halton", true},
		{"Greater Toronto Area", true},
		{"Waterloo", false},
		{"Ottawa", false},
		{"", false},
	}

	for _, tt := range tests {
		t.Run(tt.region, func(t *testing.T) {
			result := isGTARegion(tt.region)
			if result != tt.expected {
				t.Errorf("isGTARegion(%q) = %v, expected %v", tt.region, result, tt.expected)
			}
		})
	}
}

func TestIsResidentialProperty(t *testing.T) {
	tests := []struct {
		propertyType string
		expected     bool
	}{
		{"Residential", true},
		{"residential", true},
		{"RESIDENTIAL", true},
		{"Condo", true},
		{"condo", true},
		{"CONDO", true},
		{"Residential Condo", true},
		{"Commercial", false},
		{"Industrial", false},
		{"", false},
	}

	for _, tt := range tests {
		t.Run(tt.propertyType, func(t *testing.T) {
			result := isResidentialProperty(tt.propertyType)
			if result != tt.expected {
				t.Errorf("isResidentialProperty(%q) = %v, expected %v", tt.propertyType, result, tt.expected)
			}
		})
	}
}

// Helper function for absolute value
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}
