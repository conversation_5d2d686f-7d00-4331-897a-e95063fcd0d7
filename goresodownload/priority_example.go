package goresodownload

import (
	"fmt"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

// ExamplePriorityCalculation demonstrates how the priority calculator works
func ExamplePriorityCalculation() {
	fmt.Println("=== Property Priority Calculator Examples ===")
	fmt.Println()

	// Example 1: High priority property (no photos, active, GTA, new)
	fmt.Println("Example 1: High Priority Property")
	fmt.Println("- No existing photos")
	fmt.Println("- Active status")
	fmt.Println("- Current year listing")
	fmt.Println("- Toronto (GTA region)")
	fmt.Println("- New listing (today)")
	fmt.Println("- Residential property")
	fmt.Println("- Ontario province")
	fmt.Println("- 5 days on market")

	record1 := bson.M{
		"MlsStatus":           "Active",
		"ListingContractDate": time.Now().Format("2006-01-02"),
		"CountyOrParish":      "Toronto",
		"PropertyType":        "Residential",
		"StateOrProvince":     "ON",
		"DaysOnMarket":        5,
	}

	priority1 := CalculatePriority(record1, "TRB", nil) // nil = no existing merged prop = no photos
	fmt.Printf("Calculated Priority: %d\n", priority1)
	fmt.Printf("Breakdown: 30000 (no photos) + 10000 (active) + 5000 (current year) + 500 (GTA) + 300 (new) + 200 (residential) + 20 (ON) + 25 (DOM bonus) = %d\n", 30000+10000+5000+500+300+200+20+25)
	fmt.Println()

	// Example 2: Medium priority property (has photos, sold)
	fmt.Println("Example 2: Medium Priority Property")
	fmt.Println("- Has existing photos")
	fmt.Println("- Sold status")
	fmt.Println("- Old listing (2023)")
	fmt.Println("- Toronto (GTA region)")
	fmt.Println("- Residential property")
	fmt.Println("- Ontario province")
	fmt.Println("- 15 days on market")

	record2 := bson.M{
		"MlsStatus":           "Sold",
		"ListingContractDate": "2023-06-01",
		"CountyOrParish":      "Toronto",
		"PropertyType":        "Residential",
		"StateOrProvince":     "ON",
		"DaysOnMarket":        15,
	}

	existingMerged2 := bson.M{
		"media": []interface{}{
			bson.M{"key": "photo1", "url": "http://example.com/1.jpg"},
		},
	}

	priority2 := CalculatePriority(record2, "TRB", existingMerged2)
	fmt.Printf("Calculated Priority: %d\n", priority2)
	fmt.Printf("Breakdown: 0 (has photos) + 0 (not active) + 0 (old year) + 500 (GTA) + 0 (old listing) + 200 (residential) + 50 (sold) + 20 (ON) + 15 (DOM bonus) = %d\n", 500+200+50+20+15)
	fmt.Println()

	// Example 3: Low priority property (has photos, expired, non-GTA)
	fmt.Println("Example 3: Low Priority Property")
	fmt.Println("- Has existing photos")
	fmt.Println("- Expired status")
	fmt.Println("- Current year listing")
	fmt.Println("- Waterloo (non-GTA region)")
	fmt.Println("- Condo property")
	fmt.Println("- Ontario province")
	fmt.Println("- 45 days on market (no DOM bonus)")

	record3 := bson.M{
		"MlsStatus":           "Expired",
		"ListingContractDate": time.Now().Format("2006-01-02"),
		"CountyOrParish":      "Waterloo",
		"PropertyType":        "Condo",
		"StateOrProvince":     "ON",
		"DaysOnMarket":        45,
	}

	existingMerged3 := bson.M{
		"Media": []interface{}{
			bson.M{"MediaKey": "photo1", "MediaURL": "http://example.com/1.jpg"},
		},
	}

	priority3 := CalculatePriority(record3, "CAR", existingMerged3)
	fmt.Printf("Calculated Priority: %d\n", priority3)
	fmt.Printf("Breakdown: 0 (has photos) + 0 (not active) + 5000 (current year) + 0 (non-GTA) + 300 (new) + 200 (condo) + 0 (not sold/leased) + 20 (ON) + 0 (DOM > 30) = %d\n", 5000+300+200+20)
	fmt.Println()

	// Example 4: Different board types
	fmt.Println("Example 4: Board-Specific Field Mappings")

	// DDF board example
	recordDDF := bson.M{
		"MlsStatus":              "Active",
		"OriginalEntryTimestamp": time.Now().Format("2006-01-02"),
		"CityRegion":             "Durham",
		"PropertySubType":        "Condo",
		"StateOrProvince":        "ON",
	}

	priorityDDF := CalculatePriority(recordDDF, "DDF", nil)
	fmt.Printf("DDF Board Priority: %d\n", priorityDDF)

	// EDM board example
	recordEDM := bson.M{
		"MlsStatus":              "Leased",
		"ListingContractDate":    "2024-01-01",
		"StateRegion":            "Alberta",
		"PropertyType":           "Residential",
		"StateOrProvince":        "AB",
		"CumulativeDaysOnMarket": 20,
	}

	priorityEDM := CalculatePriority(recordEDM, "EDM", nil)
	fmt.Printf("EDM Board Priority: %d\n", priorityEDM)

	fmt.Println()
	fmt.Println("=== Priority Scoring System ===")
	fmt.Println("No photos:           +30,000 (highest priority)")
	fmt.Println("Active status:       +10,000")
	fmt.Println("Current year:        +5,000")
	fmt.Println("GTA region:          +500")
	fmt.Println("New listing (3d):    +300")
	fmt.Println("Residential/Condo:   +200")
	fmt.Println("Sold status:         +50")
	fmt.Println("Leased status:       +30")
	fmt.Println("Ontario province:    +20")
	fmt.Println("Days on market:      +0 to +30 (30-DOM for 0-30 days)")
}

// GetPriorityBreakdown returns a detailed breakdown of how priority was calculated
func GetPriorityBreakdown(record bson.M, boardType string, existMergedProp bson.M) map[string]int {
	breakdown := make(map[string]int)

	// Normalize property data
	normalized, err := normalizePropertyData(record, boardType)
	if err != nil {
		breakdown["error"] = 1000
		return breakdown
	}

	// Calculate each component
	hasPhoto := checkHasPhoto(existMergedProp, boardType)
	if !hasPhoto {
		breakdown["no_photos"] = 30000
	}

	if isActiveStatus(normalized.MlsStatus) {
		breakdown["active_status"] = 10000
	}

	if normalized.OnD != nil && normalized.OnD.Year() == time.Now().Year() {
		breakdown["current_year"] = 5000
	}

	if isGTARegion(normalized.Region) {
		breakdown["gta_region"] = 500
	}

	if normalized.OnD != nil && time.Since(*normalized.OnD) < 3*24*time.Hour {
		breakdown["new_listing"] = 300
	}

	if isResidentialProperty(normalized.PropertyType) {
		breakdown["residential"] = 200
	}

	if strings.ToLower(normalized.MlsStatus) == "sold" {
		breakdown["sold_status"] = 50
	} else if strings.ToLower(normalized.MlsStatus) == "leased" {
		breakdown["leased_status"] = 30
	}

	if normalized.Province == "ON" {
		breakdown["ontario"] = 20
	}

	if normalized.DOM >= 0 && normalized.DOM <= 30 {
		breakdown["dom_bonus"] = 30 - normalized.DOM
	}

	return breakdown
}
